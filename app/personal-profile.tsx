import LoadingScreen from "@/components/LoadingView";
import UserProfileForm from "@/forms/personal-information/user-profile-form";
import { useUserProfileQuery } from "@/generated/graphql";
import { useSession } from "@/providers/auth-provider";
import React, { useEffect, useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  ActivityIndicator,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { Colors } from "@/constants/Colors";
import { useNavigation } from "@react-navigation/native";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
} from "react-native-reanimated";
import * as Haptics from "expo-haptics";

export default function PersonalProfile() {
  const { session } = useSession();
  const navigation = useNavigation();
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Animation values
  const rotateValue = useSharedValue(0);

  const userId = session?.userId || "";

  const {
    data: userProfile,
    isLoading,
    refetch,
  } = useUserProfileQuery({
    userId: userId,
  });

  // Animated style for refresh icon
  const animatedIconStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${rotateValue.value}deg` }],
  }));

  // Handle refresh
  const handleRefresh = async () => {
    // Add haptic feedback
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    setIsRefreshing(true);
    rotateValue.value = withSpring(360, { duration: 800 });

    try {
      await refetch();
    } finally {
      setIsRefreshing(false);
      // Reset rotation for next use
      setTimeout(() => {
        rotateValue.value = 0;
      }, 800);
    }
  };

  // Refresh button component
  const RefreshButton = () => (
    <TouchableOpacity
      onPress={handleRefresh}
      disabled={isRefreshing}
      style={{
        padding: 8,
        marginRight: 8,
      }}
    >
      {isRefreshing ? (
        <ActivityIndicator size="small" color={Colors.white} />
      ) : (
        <Animated.View style={animatedIconStyle}>
          <Ionicons name="refresh" size={24} color={Colors.white} />
        </Animated.View>
      )}
    </TouchableOpacity>
  );

  // Set header right button
  useEffect(() => {
    navigation.setOptions({
      headerRight: () => <RefreshButton />,
    });
  }, [navigation, isRefreshing]);

  const initialData = {
    ic: userProfile?.userProfile?.ic ?? "",
    ID: userProfile?.userProfile?.ID ?? "",
    passport: userProfile?.userProfile?.passport ?? "",
    passportExpiresAt: userProfile?.userProfile?.passportExpiresAt
      ? new Date(userProfile.userProfile.passportExpiresAt)
      : undefined,
    permitNumber: userProfile?.userProfile?.permitNumber ?? "",
    permitExpiresAt: userProfile?.userProfile?.permitExpiresAt
      ? new Date(userProfile.userProfile.permitExpiresAt)
      : undefined,
    gender:
      (userProfile?.userProfile?.gender as
        | "male"
        | "female"
        | "other"
        | undefined) ?? undefined,
    dob: userProfile?.userProfile?.dob
      ? new Date(userProfile.userProfile.dob)
      : undefined,
    placeOfBirth: userProfile?.userProfile?.placeOfBirth ?? "",
    currentAddress: userProfile?.userProfile?.currentAddress ?? "",
    joinedAt: userProfile?.userProfile?.joinedAt
      ? new Date(userProfile.userProfile.joinedAt)
      : undefined,
    maritalStatus:
      (userProfile?.userProfile?.maritalStatus as
        | "single"
        | "married"
        | "divorced"
        | "widowed"
        | undefined) ?? undefined,
    bankAccNumber: userProfile?.userProfile?.bankAccNumber ?? "",
    bankName: userProfile?.userProfile?.bankName ?? "",
    emergencyContact: userProfile?.userProfile?.emergencyContact ?? [],
  };

  return (
    <View style={styles.container}>
      <Text style={styles.heading}>
        Fill the form to update your personal profile.
      </Text>
      {isLoading ? (
        <LoadingScreen />
      ) : (
        <UserProfileForm userId={userId} initialData={initialData} />
      )}
    </View>
  );
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingVertical: 8,
  },
  heading: {
    fontSize: 18,
    fontWeight: "500",
    marginBottom: 16,
    color: "#333",
    textDecorationLine: "underline",
    textDecorationStyle: "solid",
    textDecorationColor: "#333",
    paddingLeft: 16,
  },
});

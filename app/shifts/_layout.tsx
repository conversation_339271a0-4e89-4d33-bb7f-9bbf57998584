import { Stack } from "expo-router";
import { Colors } from "@/constants/Colors";

export default function ShiftsLayout() {
  return (
    <Stack
      screenOptions={{
        headerStyle: {
          backgroundColor: Colors.primary,
        },
        headerTintColor: Colors.white,
        headerShadowVisible: false,
        headerShown: false, // Hide the default header since we're using custom headers
      }}
    >
      <Stack.Screen name="index" options={{ headerShown: false }} />
      <Stack.Screen name="[id]" options={{ headerShown: false }} />
      <Stack.Screen name="clock-in-scan" options={{ headerShown: false }} />
    </Stack>
  );
}

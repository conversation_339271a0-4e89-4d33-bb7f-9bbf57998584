import { ObjectType, Field, Int, registerEnumType } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Types } from 'mongoose';
import { User } from 'src/users/entities/user.entity';
import { Location } from 'src/location/entities/location.entity';
import { ClaimUnion } from '../dto/claim-union.type';
import { MongooseSchema } from 'src/common/common.entity';

export enum ClaimStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
}

registerEnumType(ClaimStatus, { name: 'ClaimStatus' });

export enum ClaimType {
  ALLOWANCE = 'ALLOWANCE',
  TRAVEL = 'TRAVEL',
  EXPENSE = 'EXPENSE',
  SITE = 'SITE',
}

registerEnumType(ClaimType, { name: 'ClaimType' });

@ObjectType({ description: 'Common fields for all claims' })
@Schema()
class CommonClaim {
  @Field(() => Number, { description: 'Claim amount', nullable: true })
  @Prop()
  amount?: number;

  @Field(() => String, { description: 'Purpose of the claim', nullable: true })
  @Prop()
  purpose?: string;
}

@ObjectType()
@Schema()
export class Claims extends MongooseSchema {
  @Field(() => ClaimStatus, { description: 'Claim status', nullable: true })
  @Prop({ default: ClaimStatus.PENDING, enum: ClaimStatus })
  status: ClaimStatus;

  @Field(() => User, { description: 'Claim created by' })
  @Prop({ required: true, type: mongoose.Schema.Types.ObjectId, ref: 'User' })
  user: string;

  @Field(() => ClaimType, { description: 'Type of the claim' })
  @Prop({ required: true, enum: ClaimType })
  claimType: ClaimType;

  @Field(() => ClaimUnion, { description: 'claim data in calims' })
  @Prop({ required: true, type: mongoose.Schema.Types.Mixed })
  claimData: AllowanceClaim | TravelClaim | ExpenseClaim | SiteClaim;

  @Field(() => User, { description: 'Claim processed by', nullable: true })
  @Prop({ required: false, type: mongoose.Schema.Types.ObjectId, ref: 'User' })
  processedBy: string;

  @Field(() => String, { nullable: true })
  @Prop({ required: false, type: mongoose.Schema.Types.ObjectId })
  rejectedReason: string;
}

@ObjectType()
@Schema()
export class AllowanceClaim extends CommonClaim {
  @Field(() => Date)
  @Prop({ required: true })
  from: Date;

  @Field(() => Date)
  @Prop({ required: true })
  to: Date;

  @Field(() => Int)
  @Prop({ required: true })
  workingHours: number;

  @Field(() => [String], { nullable: true })
  @Prop([String])
  receipts?: string[];
}

@ObjectType()
@Schema()
export class ExpenseClaim extends CommonClaim {
  @Field(() => [String], { description: 'List of items' })
  @Prop({ required: true, type: [String] })
  items: string[];

  @Field(() => Date)
  @Prop({ required: true })
  date: Date;

  @Field(() => [String])
  @Prop({ required: true, type: [String] })
  receipts: string[];
}

@ObjectType()
@Schema()
export class TravelClaim extends CommonClaim {
  @Field(() => Date)
  @Prop({ required: true })
  from: Date;

  @Field(() => Date)
  @Prop({ required: true })
  to: Date;

  @Field(() => String)
  @Prop({ required: true })
  client: string;

  @Field(() => String)
  @Prop({ required: true })
  toll: string;

  @Field(() => Int)
  @Prop({ required: true })
  distance: number;

  @Field(() => [String])
  @Prop({ required: true, type: [String] })
  receipts: string[];
}

@ObjectType()
@Schema()
export class SiteClaim extends CommonClaim {
  @Field(() => Location)
  @Prop({ required: true, type: Types.ObjectId, ref: 'Location' })
  site: string;

  @Field(() => [String])
  @Prop({ required: true, type: [String] })
  items: string[];

  @Field(() => [String])
  @Prop({ required: true, type: [String] })
  receipts: string[];
}

export const ClaimsSchema = SchemaFactory.createForClass(Claims);
